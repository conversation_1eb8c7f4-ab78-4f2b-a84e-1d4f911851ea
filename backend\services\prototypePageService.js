const { pool } = require('./promptDbService');

/**
 * PrototypePageService - Manages permanent pages for prototypes
 * 
 * This service handles CRUD operations for prototype pages that are permanently
 * stored and linked to prototypes (not temporary sessions).
 */

class PrototypePageService {
  
  /**
   * Create a new page for a prototype
   * @param {Object} pageData - Page data
   * @param {number} pageData.prototype_id - Prototype ID
   * @param {number} pageData.user_id - User ID
   * @param {string} pageData.title - Page title
   * @param {string} pageData.html_content - HTML content
   * @param {string} pageData.css_content - CSS content (optional)
   * @param {boolean} pageData.is_default - Whether this is the default page
   * @returns {Promise<Object>} Created page
   */
  async createPage(pageData) {
    const {
      prototype_id,
      user_id,
      title,
      html_content,
      css_content = null,
      is_default = false
    } = pageData;

    console.log(`[PrototypePageService] Creating page for prototype ${prototype_id}`);

    try {
      // If this is set as default, unset other default pages for this prototype
      if (is_default) {
        await this._unsetDefaultPages(prototype_id);
      }

      // Get the next page order
      const pageOrder = await this._getNextPageOrder(prototype_id);

      const query = `
        INSERT INTO prototype_pages (
          prototype_id, user_id, title, html_content, css_content, 
          page_order, is_default
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `;

      const values = [
        prototype_id, user_id, title, html_content, css_content,
        pageOrder, is_default
      ];

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        throw new Error('Failed to create page');
      }

      console.log(`[PrototypePageService] Page created successfully: ${result.rows[0].id}`);
      return result.rows[0];

    } catch (error) {
      console.error(`[PrototypePageService] Error creating page:`, error);
      throw error;
    }
  }

  /**
   * Get all pages for a prototype with pagination
   * @param {number} prototypeId - Prototype ID
   * @param {number} userId - User ID for security
   * @param {number} limit - Maximum number of pages to return
   * @param {number} offset - Number of pages to skip
   * @returns {Promise<Array>} Array of pages
   */
  async getPagesByPrototypePaginated(prototypeId, userId, limit = 30, offset = 0) {
    console.log(`[PrototypePageService] Getting paginated pages for prototype ${prototypeId}, user ${userId}`);

    try {
      const query = `
        SELECT * FROM prototype_pages
        WHERE prototype_id = $1 AND user_id = $2
        ORDER BY page_order ASC, created_at ASC
        LIMIT $3 OFFSET $4
      `;

      const result = await pool.query(query, [prototypeId, userId, limit, offset]);
      return result.rows;

    } catch (error) {
      console.error(`[PrototypePageService] Error getting paginated pages:`, error);
      throw error;
    }
  }

  /**
   * Get total count of pages for a prototype
   * @param {number} prototypeId - Prototype ID
   * @param {number} userId - User ID for security
   * @returns {Promise<number>} Total count of pages
   */
  async getPagesCountByPrototype(prototypeId, userId) {
    try {
      const query = `
        SELECT COUNT(*) as count FROM prototype_pages
        WHERE prototype_id = $1 AND user_id = $2
      `;

      const result = await pool.query(query, [prototypeId, userId]);
      return parseInt(result.rows[0].count, 10);

    } catch (error) {
      console.error(`[PrototypePageService] Error getting pages count:`, error);
      throw error;
    }
  }

  /**
   * Get a specific page by ID
   * @param {number} pageId - Page ID
   * @param {number} userId - User ID for security
   * @returns {Promise<Object|null>} Page object or null if not found
   */
  async getPageById(pageId, userId) {
    try {
      const query = `
        SELECT * FROM prototype_pages
        WHERE id = $1 AND user_id = $2
      `;

      const result = await pool.query(query, [pageId, userId]);
      return result.rows[0] || null;

    } catch (error) {
      console.error(`[PrototypePageService] Error getting page by ID:`, error);
      throw error;
    }
  }

  /**
   * Update a page
   * @param {number} pageId - Page ID
   * @param {number} userId - User ID for security
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated page or null if not found
   */
  async updatePage(pageId, userId, updateData) {
    const allowedFields = ['title', 'html_content', 'css_content', 'is_default', 'page_order'];
    const fields = Object.keys(updateData).filter(key => allowedFields.includes(key));
    
    if (fields.length === 0) {
      return null;
    }

    try {
      // If setting as default, unset other defaults first
      if (updateData.is_default) {
        const page = await this.getPageById(pageId, userId);
        if (page) {
          await this._unsetDefaultPages(page.prototype_id);
        }
      }

      const setClause = fields.map((field, index) => `${field} = $${index + 3}`).join(', ');
      const values = fields.map(field => updateData[field]);

      const query = `
        UPDATE prototype_pages 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2
        RETURNING *
      `;

      const result = await pool.query(query, [pageId, userId, ...values]);
      return result.rows[0] || null;

    } catch (error) {
      console.error(`[PrototypePageService] Error updating page:`, error);
      throw error;
    }
  }

  /**
   * Delete a page
   * @param {number} pageId - Page ID
   * @param {number} userId - User ID for security
   * @returns {Promise<boolean>} True if deleted, false if not found
   */
  async deletePage(pageId, userId) {
    try {
      const query = `
        DELETE FROM prototype_pages
        WHERE id = $1 AND user_id = $2
      `;

      const result = await pool.query(query, [pageId, userId]);
      return result.rowCount > 0;

    } catch (error) {
      console.error(`[PrototypePageService] Error deleting page:`, error);
      throw error;
    }
  }

  /**
   * Get the default page for a prototype
   * @param {number} prototypeId - Prototype ID
   * @param {number} userId - User ID for security
   * @returns {Promise<Object|null>} Default page or null if not found
   */
  async getDefaultPage(prototypeId, userId) {
    try {
      const query = `
        SELECT * FROM prototype_pages
        WHERE prototype_id = $1 AND user_id = $2 AND is_default = TRUE
        LIMIT 1
      `;

      const result = await pool.query(query, [prototypeId, userId]);
      return result.rows[0] || null;

    } catch (error) {
      console.error(`[PrototypePageService] Error getting default page:`, error);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Unset default flag for all pages in a prototype
   * @param {number} prototypeId - Prototype ID
   * @private
   */
  async _unsetDefaultPages(prototypeId) {
    try {
      const query = `
        UPDATE prototype_pages 
        SET is_default = FALSE 
        WHERE prototype_id = $1 AND is_default = TRUE
      `;

      await pool.query(query, [prototypeId]);

    } catch (error) {
      console.error(`[PrototypePageService] Error unsetting default pages:`, error);
      throw error;
    }
  }

  /**
   * Get the next page order number for a prototype
   * @param {number} prototypeId - Prototype ID
   * @returns {Promise<number>} Next page order number
   * @private
   */
  async _getNextPageOrder(prototypeId) {
    try {
      const query = `
        SELECT COALESCE(MAX(page_order), 0) + 1 as next_order
        FROM prototype_pages
        WHERE prototype_id = $1
      `;

      const result = await pool.query(query, [prototypeId]);
      return result.rows[0].next_order;

    } catch (error) {
      console.error(`[PrototypePageService] Error getting next page order:`, error);
      return 1; // Default to 1 if error
    }
  }
}

module.exports = new PrototypePageService();
